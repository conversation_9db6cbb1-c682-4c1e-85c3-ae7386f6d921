<?php
session_start();
include_once '../lib/config.php';
?>
<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<title>Forgot Password - <?php echo SITE_NAME; ?></title>
<meta name="viewport"
		content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
<link rel="icon" type="image/png" href="./assets/fav.png" sizes="96x96" />
<link rel="stylesheet" href="./assets/cy/libs/bootstrap/css/bootstrap.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/priority-navigation-master/dist/priority-nav-core.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/swiper/swiper.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/bootstrap-select-1.14.0/dist/css/bootstrap-select.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/plyr/dist/plyr.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/ion.rangeSlider-master/css/ion.rangeSlider.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/toastr-master/build/toastr.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/css/main.css?vs=100">
<link rel="stylesheet" href="./assets/cy/css/media.css?vs=100">
<style>
.openUserInfo {
	cursor: pointer;
}
.sectionLotteriesItemsSMEmpty {
	min-height: 46px;
}
button.change-pswd-type-link {
	border: 0px;
}
.logo-img img {
  width: 160px;
}
.mobile-panel-top-left .logo-wrapper {
  display: flex;
  max-width: 160px;
}
.hidden { display: none; }
.margin-top { margin-top: 15px; }
</style>

<link rel="shortcut icon" href="favicon.ico" />
</head>

<body class="page page--main">
<div class="page-inner">
<div class="page-progress-block xProgress d-none">
  <div class="page-progress">
    <div class="page-progress__progress xProgressPercent" style="width: 0%;"></div>
  </div>
</div>
<div class="mobile-panel-block">
  <div class="mobile-panel-top-block">
    <div class="mobile-panel-top-left">
      <div class="logo-wrapper"> <a href="index.php" class="logo">
        <div class="logo-img"> <img class="image" src="assets/logo.png" alt=""> </div>
        </a> </div>
    </div>
    <div class="mobile-panel-top-right">
      <div class="topline-lang-panel-block">
        <div class="topline-lang-panel"> <a href="javascript:void(0)" class="current-lang" role="button" data-bs-toggle="dropdown"
								aria-expanded="false">
          <div class="current-lang__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
          <div class="current-lang__text">EN</div>
          </a>
          <div class="topline-lang-dropdown dropdown-menu dropdown-menu-end"> <a href="?lang=default" class="lang-link">
            <div class="lang-link__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
            <div class="lang-link__text">EN</div>
            </a> </div>
        </div>
      </div>
      <div class="mobile-panel-close-btn-block">
        <button type="button" class="mobile-panel-close-btn"></button>
      </div>
    </div>
  </div>
  <div class="mobile-panel-menu-block">
    <div class="mobile-panel-menu">
      <div class="mobile-auth-panel-block">
        <div class="mobile-auth-panel">
          <div class="mobile-auth-panel-btn-block">
            <a  href='index.php' class="topline-login-btn">
            <div class="topline-login-btn__text">Sign In</div>
            <div class="topline-login-btn__icon"></div>
            </a>
          </div>
          <div class="mobile-auth-panel-btn-block"> <a href="register.php" class="topline-registration-btn">
            <div class="topline-registration-btn__text">Register</div>
            <div class="topline-registration-btn__icon"></div>
            </a> </div>
        </div>
      </div>
      <div class="mobile-menu">
        <ul>
          <li class="mobile-menu-item mobile-menu-item--games"> <a href="https://tether50.com/about.php" class="mobile-menu-link mobile-menu-link--games">
            <div class="mobile-menu-link__text"> About Us </div>
            </a> </li>
          <li class="mobile-menu-item mobile-menu-item--lottery"> <a href="https://tether50.com/index.php#help" class="mobile-menu-link mobile-menu-link--lottery">
            <div class="mobile-menu-link__text"> Help </div>
            </a> </li>
          <li class="mobile-menu-item mobile-menu-item--lottery"> <a href="https://tether50.com/index.php#contact" class="mobile-menu-link mobile-menu-link--lottery">
            <div class="mobile-menu-link__text"> Contact </div>
            </a> </li>
        </ul>
      </div>
    </div>
  </div>
</div>

<header class="header">
  <div class="container">
    <div class="row">
      <div class="col-12">
        <div class="header-block">
          <div class="header-left">
            <div class="logo-wrapper"> <a href="index.php" class="logo">
              <div class="logo-img"> <img class="image" src="assets/logo.png" alt=""> </div>
              </a> </div>
          </div>
          <div class="header-center">
            <div class="header-menu-block">
              <div class="header-menu">
                <ul>
                  <li class="header-menu-item header-menu-item--games"> <a href="https://tether50.com/about.php" class="header-menu-link header-menu-link--games">
                    <div class="header-menu-link__text"> About Us </div>
                    </a> </li>
                  <li class="header-menu-item header-menu-item--lottery"> <a href="https://tether50.com/index.php#help" class="header-menu-link header-menu-link--lottery">
                    <div class="header-menu-link__text"> Help </div>
                    </a> </li>
                  <li class="header-menu-item header-menu-item--lottery"> <a href="https://tether50.com/index.php#contact" class="header-menu-link header-menu-link--lottery">
                    <div class="header-menu-link__text"> Contact </div>
                    </a> </li>
                </ul>
              </div>
            </div>
          </div>
          <div class="header-right">
            <div class="topline-block">
              <div class="topline">
                <div class="topline-left">
                  <div class="topline-lang-panel-block">
                    <div class="topline-lang-panel"> <a href="javascript:void(0)" class="current-lang" role="button" data-bs-toggle="dropdown"
										aria-expanded="false">
                      <div class="current-lang__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
                      <div class="current-lang__text">EN</div>
                      </a>
                      <div class="topline-lang-dropdown dropdown-menu dropdown-menu-end"> <a href="?lang=default" class="lang-link">
                        <div class="lang-link__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
                        <div class="lang-link__text">EN</div>
                        </a> </div>
                    </div>
                  </div>
                </div>
                <div class="topline-right">
                  <div class="topline-auth-panel-block">
                    <div class="topline-auth-panel">
                      <div class="topline-login-btn-block">
                        <a href='index.php' class="topline-login-btn">
                        <div class="topline-login-btn__text">Sign In</div>
                        <div class="topline-login-btn__icon"></div>
                        </a>
                      </div>
                      <div class="topline-registration-btn-block"> <a href="register.php" class="topline-registration-btn">
                        <div class="topline-registration-btn__text">Register</div>
                        <div class="topline-registration-btn__icon"></div>
                        </a> </div>
                      <div class="mobile-panel-btn-block">
                        <button type="button" class="mobile-panel-btn"></button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</header>

<style>
.field--contact .field-icon::before{background-color:transparent;}
.field--contact  img{margin-top:-10px;}

</style>
<section class="section-registration">
  <div class="container">
    <div class="row">
      <div class="col-12">
        <div class="registration-block">
          <div class="registration">
            <div class="registration-left">
              <div class="registration-feature-items-block">
                <div class="registration-feature-items">
                  <div class="registration-feature-item-wrapper">
                    <div class="registration-feature-item">
                      <div class="registration-feature-item__icon"> <img class="image" src="assets/cy/images/svg/registration/registration-feature-item-icon--dice-five.svg" alt=""> </div>
                      <div class="registration-feature-item__descr"> Exciting and fair games and lotteries </div>
                    </div>
                  </div>
                  <div class="registration-feature-item-wrapper">
                    <div class="registration-feature-item">
                      <div class="registration-feature-item__icon"> <img class="image" src="assets/cy/images/svg/registration/registration-feature-item-icon--checkerboard.svg" alt=""> </div>
                      <div class="registration-feature-item__descr"> Staking offers with returns of up to 200% </div>
                    </div>
                  </div>
                  <div class="registration-feature-item-wrapper">
                    <div class="registration-feature-item">
                      <div class="registration-feature-item__icon"> <img class="image" src="assets/cy/images/svg/registration/registration-feature-item-icon--shield-check.svg" alt=""> </div>
                      <div class="registration-feature-item__descr"> We ensure your security and anonymity </div>
                    </div>
                  </div>
                  <div class="registration-feature-item-wrapper">
                    <div class="registration-feature-item">
                      <div class="registration-feature-item__icon"> <img class="image" src="assets/cy/images/svg/registration/registration-feature-item-icon--rocket-launch.svg" alt=""> </div>
                      <div class="registration-feature-item__descr"> Join our generous affiliate program </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="registration-left-bg-image"> <img class="image" src="assets/cy/images/theme/registration-left-image.png" alt=""> </div>
            </div>
            <div class="registration-right">
              <div class="registration-form-block">
                <h2>Forgot Password</h2>

                <!-- STEP 1: Enter User ID and Email -->
                <div id="step1">
                  <form id="userDetailsForm" class="registration-form form">
                    <div class="field-block">
                      <div class="field-title-block">
                        <div class="field-title">User ID <span class="field-required-star">*</span> </div>
                      </div>
                      <div class="field field--input field--have-icon field--username">
                        <div class="field-icon"></div>
                        <input type="text" id="login_id" name="login_id" maxlength="20" autocomplete="off" placeholder="Enter your User ID" required>
                      </div>
                    </div>
                    <div class="field-block">
                      <div class="field-title-block">
                        <div class="field-title">Email <span class="field-required-star">*</span> </div>
                      </div>
                      <div class="field field--input field--have-icon field--email">
                        <div class="field-icon"></div>
                        <input type="email" id="email" name="email" maxlength="100" autocomplete="off" placeholder="Enter your Email" required>
                      </div>
                    </div>
                    <div class="form-button-block">
                      <button type="button" id="sendOtpBtn" class="green-gr-btn send-btn">
                        <div class="send-btn__text">Send OTP</div>
                        <div class="send-btn__icon"></div>
                      </button>
                    </div>
                    <div id="userDetailsError" class="alert alert-danger hidden margin-top"></div>
                  </form>
                </div>

                <!-- STEP 2: OTP Verification -->
                <div id="step2" class="hidden">
                  <form id="otpForm" class="registration-form form">
                    <div class="field-block">
                      <div class="field-title-block">
                        <div class="field-title">OTP <span class="field-required-star">*</span> </div>
                      </div>
                      <div class="field field--input field--have-icon field--password">
                        <div class="field-icon"></div>
                        <input type="text" id="otp" name="otp" maxlength="6" autocomplete="off" placeholder="Enter OTP" required>
                      </div>
                    </div>
                    <div class="form-button-block">
                      <button type="button" id="verifyOtpBtn" class="green-gr-btn send-btn">
                        <div class="send-btn__text">Verify OTP</div>
                        <div class="send-btn__icon"></div>
                      </button>
                    </div>
                    <div id="otpError" class="alert alert-danger hidden margin-top"></div>
                  </form>
                </div>

                <!-- STEP 3: Create New Password -->
                <div id="step3" class="hidden">
                  <form id="changePasswordForm" action="forgot_model.php?step=3" method="post" novalidate class="registration-form form">
                    <div class="field-block">
                      <div class="field-title-block">
                        <div class="field-title">New Password <span class="field-required-star">*</span> </div>
                      </div>
                      <div class="field field--input field--have-icon field--password">
                        <div class="field-icon"></div>
                        <input type="password" name="password" maxlength="20" autocomplete="off" placeholder="Enter new password" required>
                        <div class="field-right-panel-block">
                          <div class="field-right-panel">
                            <div class="change-pswd-type-link-block">
                              <button type="button" class="change-pswd-type-link"></button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="field-block">
                      <div class="field-title-block">
                        <div class="field-title">Confirm New Password <span class="field-required-star">*</span> </div>
                      </div>
                      <div class="field field--input field--have-icon field--password">
                        <div class="field-icon"></div>
                        <input type="password" name="confirm_password" maxlength="20" autocomplete="off" placeholder="Confirm new password" required>
                        <div class="field-right-panel-block">
                          <div class="field-right-panel">
                            <div class="change-pswd-type-link-block">
                              <button type="button" class="change-pswd-type-link"></button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <!-- Pass along the login_id and email for final submission -->
                    <input type="hidden" name="login_id" id="hidden_login_id">
                    <input type="hidden" name="email" id="hidden_email">
                    <div class="form-button-block">
                      <button type="submit" id="finalSubmitBtn" class="green-gr-btn send-btn">
                        <div class="send-btn__text">Submit</div>
                        <div class="send-btn__icon"></div>
                      </button>
                    </div>
                    <div id="changePwdError" class="alert alert-danger hidden margin-top"></div>
                  </form>
                </div>

                <div class="form-bottom-note-block">
                  <div class="form-bottom-note"> Remember your password? <a href="index.php">Sign In</a> </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="registration-mobile-feature-items-block">
          <div class="registration-mobile-feature-items">
            <div class="registration-mobile-feature-item-wrapper">
              <div class="registration-mobile-feature-item">
                <div class="registration-mobile-feature-item__icon"> <img class="image" src="assets/cy/images/svg/registration/registration-feature-item-icon--dice-five.svg" alt=""> </div>
                <div class="registration-mobile-feature-item__descr"> Exciting and fair games and lotteries </div>
              </div>
            </div>
            <div class="registration-mobile-feature-item-wrapper">
              <div class="registration-mobile-feature-item">
                <div class="registration-mobile-feature-item__icon"> <img class="image" src="assets/cy/images/svg/registration/registration-feature-item-icon--checkerboard.svg" alt=""> </div>
                <div class="registration-mobile-feature-item__descr"> Staking offers with returns of up to 200% </div>
              </div>
            </div>
            <div class="registration-mobile-feature-item-wrapper">
              <div class="registration-mobile-feature-item">
                <div class="registration-mobile-feature-item__icon"> <img class="image" src="assets/cy/images/svg/registration/registration-feature-item-icon--shield-check.svg" alt=""> </div>
                <div class="registration-mobile-feature-item__descr"> We ensure your security and anonymity </div>
              </div>
            </div>
            <div class="registration-mobile-feature-item-wrapper">
              <div class="registration-mobile-feature-item">
                <div class="registration-mobile-feature-item__icon"> <img class="image" src="assets/cy/images/svg/registration/registration-feature-item-icon--rocket-launch.svg" alt=""> </div>
                <div class="registration-mobile-feature-item__descr"> Join our generous affiliate program </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- jQuery and Bootstrap JS -->
<script src="./assets/cy/libs/jquery/jquery.min.js"></script>
<script src="./assets/cy/libs/bootstrap/js/bootstrap.bundle.min.js"></script>
<script src="./assets/cy/libs/priority-navigation-master/dist/priority-nav.min.js"></script>
<script src="./assets/cy/libs/swiper/swiper.min.js"></script>
<script src="./assets/cy/libs/bootstrap-select-1.14.0/dist/js/bootstrap-select.min.js"></script>
<script src="./assets/cy/libs/plyr/dist/plyr.min.js"></script>
<script src="./assets/cy/libs/ion.rangeSlider-master/js/ion.rangeSlider.min.js"></script>
<script src="./assets/cy/libs/toastr-master/build/toastr.min.js"></script>
<script src="./assets/cy/js/main.js"></script>
  <script>
    $(document).ready(function(){
      // STEP 1: Send OTP
      
      $('#sendOtpBtn').click(function(){
        // Clear any previous error messages.
        $('#userDetailsError').addClass('hidden').text('');
        
        var login_id = $('#login_id').val().trim();
        var email = $('#email').val().trim();
        
        
        if (login_id === "" || email === "") {
          $('#userDetailsError').removeClass('hidden').text('Both User ID and Email are required.');
          return;
        }
        $("#sendOtpBtn").attr("disabled", "true");
        // Save the entered values in hidden fields for later submission.
        $('#hidden_login_id').val(login_id);
        $('#hidden_email').val(email);
        
        $.ajax({
          url: 'send_otp.php',
          type: 'POST',
          dataType: 'json',
          data: { email: email },
          success: function(response) {
            if (response.success) {
              // Hide the Send OTP button after a successful response.
              $('#sendOtpBtn').hide();
              $('#step2').removeClass('hidden');
              // Show the OTP input and Verify OTP button.
              $('#step2').slideDown();
            } else {
              $('#userDetailsError').removeClass('hidden').text(response.error || 'Failed to send OTP.');
            }
          },
          error: function(xhr, status, error) {
            $('#userDetailsError').removeClass('hidden').text('Error: ' + error);
          }
        });
      });
      
      // STEP 2: Verify OTP
      $('#verifyOtpBtn').click(function(){
        $('#otpError').addClass('hidden').text('');
        var otp = $('#otp').val().trim();
        if (otp === "") {
          $('#otpError').removeClass('hidden').text('Please enter the OTP.');
          return;
        }
        $("#verifyOtpBtn").attr("disabled", "true");
        $.ajax({
          url: 'verify_otp.php',
          type: 'POST',
          dataType: 'json',
          data: { otp: otp },
          success: function(response) {
            if (response.success) {
              // Hide the Verify OTP button upon successful OTP verification.
              $('#verifyOtpBtn').hide();
              $('#step3').removeClass('hidden');
              // Hide the OTP input section.
              $('#step2').slideUp();
              // Show the password change section.
              $('#step3').slideDown();
            } else {
                 $("#verifyOtpBtn").removeAttr("disabled");
              $('#otpError').removeClass('hidden').text(response.error || 'OTP verification failed.');
            }
          },
          error: function(xhr, status, error) {
            $('#otpError').removeClass('hidden').text('Error: ' + error);
          }
        });
      });
      
      // Client-side validation for the password fields before final submission.
      $('#changePasswordForm').submit(function(e){
        var pwd = $(this).find('input[name="password"]').val();
        var cpwd = $(this).find('input[name="confirm_password"]').val();
        if(pwd !== cpwd){
          e.preventDefault();
          $('#changePwdError').removeClass('hidden').text('Passwords do not match.');
        }
      });
    });
  </script>
</div>
</body>
</html>
