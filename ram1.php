<?php
include_once '../lib/config.php';
user();
$uid = $_SESSION['userid'];
$user = get_user_details($uid);
$_address = strtolower(SITE_CURRENCY_) . '_address';
$typearr = array(6 => 'DOT', 7 => 'TRX', 8 => 'LINK', 9 => 'BNB', 10 => 'BTC', 11 => 'ETC');
?>
<!DOCTYPE html>
<html lang="en" data-theme="dark">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="author" content="">
    <meta name="description" content="<?php echo isset($description) ? $description : ''; ?>">
    <meta name="keywords" content="<?php echo isset($keywords) ? $keywords : ''; ?>" />
    <title>
        <?php echo $title_name = isset($title) ? SITE_NAME . ' | ' . str_replace('COIN_NAME', SITE_CURRENCY, $title) : SITE_NAME . ' | Member Panel'; ?>
    </title>
    <link rel="shortcut icon" href="images/Invesccoglobal_logo.png" type="image/x-icon">
    <script src="https://ajax.googleapis.com/ajax/libs/webfont/1.6.26/webfont.js"></script>
    <script>
        WebFont.load({
            google: {
                families: ['Alegreya+Sans:100,100i,300,300i,400,400i,500,500i,700,700i,800,800i,900,900i', 'Raleway:100,100i,200,200i,300,300i,400,400i,500,500i,600,600i,700,700i,800,800i,900,900i', 'Open Sans']
            }
        });
    </script>
    <!-- START GLOBAL MANDATORY STYLE -->
    <link href="../assets/dist/css/base.css" rel="stylesheet" type="text/css" />
    <!-- START PAGE LABEL PLUGINS -->
    <link href="../assets/plugins/datatables/dataTables.min.css" rel="stylesheet" type="text/css" />
    <?php if ($_SERVER["PHP_SELF"] == '/soft/admin/dashboard.php' || (isset($_is_dashboard) && $_is_dashboard)) { ?>
        <link href="../assets/plugins/toastr/toastr.min.css" rel=stylesheet type="text/css" />
        <link href="../assets/plugins/emojionearea/emojionearea.min.css" rel=stylesheet type="text/css" />
        <link href="../assets/plugins/monthly/monthly.min.css" rel=stylesheet type="text/css" />
        <link href="../assets/plugins/amcharts/export.css" rel=stylesheet type="text/css" />
    <?php } ?>
    <!-- START THEME LAYOUT STYLE -->
    <link href="../assets/dist/css/component_ui.min.css" rel="stylesheet" type="text/css" />
    <?php /*<link id="defaultTheme" href="../assets/dist/css/skins/component_ui_black.css" rel="stylesheet" type="text/css"/>
   <link href="../assets/dist/css/component_ui_black.css" rel="stylesheet" type="text/css"/>*/ ?>
    <link id="defaultTheme" href="../assets/dist/css/skins/skin-blue.min.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.8.2/css/all.css"
        integrity="sha384-oS3vJWv+0UjzBfQzYUhtDYW+Pj2yciDJxpsK1OYPAYjqT085Qq/1cq5FLXAZQ7Ay" crossorigin="anonymous">

    <link href="../assets/dist/css/custom.css" rel="stylesheet" type="text/css" />
    <?php /*<link id="defaultTheme" href="../assets/dist/css/skins/skin-red-dark.css" rel="stylesheet" type="text/css"/>*/ ?>
    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
            <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
            <script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
        <![endif]-->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <style>
        :root {
            /* Dark theme variables */
            --primary-bg: #0b0e11;
            --secondary-bg: #1e2329;
            --hover-bg: #2b3139;
            --text-primary: #eaecef;
            --text-secondary: #848e9c;
            --accent-color: #f0b90b;
            --border-color: #2c3137;
            --success-color: #02c076;
            --danger-color: #f6465d;
            --card-bg: #2b3139;
        }

        /* Light theme variables */
        [data-theme="light"] {
            --primary-bg: #ffffff;
            --secondary-bg: #f5f5f5;
            --hover-bg: #e8e8e8;
            --text-primary: #1e2329;
            --text-secondary: #707a8a;
            --border-color: #e8e8e8;
            --card-bg: #ffffff;
        }

        /* Animation classes */
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }

        .slide-in {
            animation: slideIn 0.3s ease-in-out;
        }

        @keyframes slideIn {
            from {
                transform: translateX(-20px);
                opacity: 0;
            }

            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* Enhanced Navbar */
        .navbar {
            background-color: var(--secondary-bg);
            border-bottom: 1px solid var(--border-color);
            height: 64px;
            padding: 0 24px;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            display: flex;
            backdrop-filter: blur(10px);
        }
@media (min-width: 768px) {
    #page-wrapper {
            position: inherit;
            margin-left: 70px;
            padding: 0 30px 30px;
        }
        .sidebar{
            width:80px;
        }
}
@media (max-width: 768px) {
        .menu-text {
    display: block;
    opacity: 1;
    transition: opacity 0.3s ease;
}
        .sidebar{
            width:auto !important;
        }
}

        .navbar-brand {
            padding: 12px 0;
            display: flex;
            align-items: center;
        }

        .navbar-brand img {
            height: 32px;
            transition: transform 0.3s ease;
        }

        .navbar-brand img:hover {
            transform: scale(1.05);
        }

        /* Enhanced Sidebar */
        .sidebar {
            width: 80px;
            position: absolute;
            height: 100%;
            left: 0;
            /*top: 64px;*/
            background-color: var(--secondary-bg);
            transition: width 0.3s ease;
            z-index: 999;
        }

        .sidebar.expanded {
            width: 260px;
        }

        /* Menu Items */
        #side-menu li {
            position: relative;
        }

        #side-menu li a {
            padding: 14px 20px;
            display: flex;
            align-items: center;
            gap: 12px;
            color: var(--text-primary);
            text-decoration: none;
            white-space: nowrap;
        }

        /* Submenu styling for collapsed state */
        .sidebar:not(.expanded) .nav-second-level {
            position: absolute;
            z-index:100000;
            
            left: 30px;
            top: 0;
            min-width: 200px;
            background: var(--card-bg);
            border-radius: 4px;
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.2);
            opacity: 0;
            visibility: hidden;
            transform: translateX(10px);
            transition: all 0.3s ease;
            padding: 8px 0;
            border: 1px solid var(--border-color);
        }

        /* Show submenu on hover */
        .sidebar:not(.expanded) #side-menu li:hover > .nav-second-level {
            opacity: 1;
            visibility: visible;
            transform: translateX(0);
        }

        /* Submenu items in collapsed state */
        .sidebar:not(.expanded) .nav-second-level li a {
            padding: 10px 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .sidebar:not(.expanded) .nav-second-level li a:hover {
            background: var(--hover-bg);
        }

        /* Submenu styling for expanded state */
        .sidebar.expanded .nav-second-level {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: rgba(0, 0, 0, 0.1);
        }

        .sidebar.expanded .nav-second-level.show {
            max-height: 500px;
        }

        /* Arrow indicator */
        .fa.arrow {
            margin-left: auto;
            transition: transform 0.3s ease;
        }

        .sidebar.expanded li a[aria-expanded="true"] .fa.arrow {
            transform: rotate(180deg);
        }

        /* Hover effect for main menu items */
        #side-menu > li > a:hover {
            background: var(--hover-bg);
        }

        /* Animation for submenu items */
        .nav-second-level li {
            opacity: 0;
            transform: translateX(-10px);
            transition: all 0.3s ease;
        }

        .nav-second-level.show li,
        .sidebar:not(.expanded) #side-menu li:hover > .nav-second-level li {
            opacity: 1;
            transform: translateX(0);
        }

        /* Delayed animation for submenu items */
        .nav-second-level li:nth-child(1) { transition-delay: 0.1s; }
        .nav-second-level li:nth-child(2) { transition-delay: 0.2s; }
        .nav-second-level li:nth-child(3) { transition-delay: 0.3s; }
        .nav-second-level li:nth-child(4) { transition-delay: 0.4s; }
        .nav-second-level li:nth-child(5) { transition-delay: 0.5s; }

        /* Tooltip for collapsed state */
        .sidebar:not(.expanded) #side-menu > li > a:hover::after {
            content: attr(data-title);
            position: absolute;
            left: 70px;
            top: 50%;
            transform: translateY(-50%);
            background: var(--card-bg);
            color: var(--text-primary);
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            white-space: nowrap;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            z-index: 1000;
            border: 1px solid var(--border-color);
        }

        /* Hide tooltip if item has submenu */
        .sidebar:not(.expanded) #side-menu > li:hover > a[data-has-submenu]::after {
            display: none;
        }

        /* Icon styling */
        #side-menu li a i {
            min-width: 24px;
            text-align: center;
            font-size: 20px;
        }

        /* Menu text */
        .menu-text {
            display: none;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .sidebar.expanded .menu-text {
            display: block;
            opacity: 1;
        }

        /* Toggle button */
        .menu-toggle {
            background: transparent;
            border: none;
            color: var(--text-primary);
            font-size: 24px;
            cursor: pointer;
            padding: 10px;
            margin-right: 15px;
            transition: all 0.3s ease;
        }

        .menu-toggle:hover {
            color: var(--accent-color);
            transform: scale(1.1);
        }

        /* Active state */
        #side-menu li a.active,
        #side-menu li a:hover {
            background: var(--hover-bg);
        }

        /* Arrow for submenu */
        .fa.arrow {
            margin-left: auto;
            font-size: 12px;
            transition: transform 0.3s ease;
            opacity: 0;
        }

        .sidebar.expanded .fa.arrow {
            opacity: 1;
        }

        /* Submenu */
        .nav-second-level {
            display: none;
            padding-left: 15px;
            background: rgba(0, 0, 0, 0.1);
        }

        .sidebar.expanded .nav-second-level {
            display: block;
        }

        .nav-second-level li a {
            padding: 10px 15px;
            font-size: 14px;
        }

        /* Arrow icon adjustments */
        .fa.arrow {
            opacity: 0;
            transition: all 0.3s ease;
        }

        .sidebar.expanded .fa.arrow {
            opacity: 1;
        }

        /* Submenu adjustments */
        .nav-second-level {
            padding-left: 0;
        }

        .sidebar.expanded .nav-second-level {
            padding-left: 15px;
        }

        /* Fix for arrow alignment */
        .fa.arrow {
            margin-left: auto;
            font-size: 12px;
            transition: transform 0.2s ease;
        }

        #side-menu li a[aria-expanded="true"] .fa.arrow {
            transform: rotate(180deg);
        }

        /* Remove any additional borders or lines */
        #side-menu li {
            border: none;
        }

        #side-menu li a:hover {
            background-color: var(--hover-bg);
        }

        /* Fix for nested menu items */
        .nav-second-level {
            padding-left: 15px;
        }

        .nav-second-level li a {
            padding-left: 30px !important;
        }

        /* Remove any unwanted borders */
        .sidebar-nav {
            border: none;
        }

        .nav.nav-second-level {
            background: transparent;
        }

        /* Theme Switcher */
        .theme-switcher {
            position: fixed;
            bottom: 24px;
            right: 24px;
            background-color: var(--card-bg);
            border-radius: 50%;
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .theme-switcher:hover {
            transform: scale(1.1);
        }

        /* Settings Panel */
        .settings-panel {
            position: fixed;
            right: -300px;
            top: 64px;
            width: 300px;
            height: calc(100vh - 64px);
            background-color: var(--card-bg);
            border-left: 1px solid var(--border-color);
            transition: right 0.3s ease;
            z-index: 998;
            padding: 24px;
        }

        .settings-panel.active {
            right: 0;
        }

        /* Top Navigation Icons */
        .navbar-top-links {
            display: flex;
            align-items: center;
            gap: 24px;
        }

        .navbar-top-links .nav-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s ease;
            position: relative;
        }

        .navbar-top-links .nav-icon:hover {
            background-color: var(--hover-bg);
        }

        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            background-color: var(--danger-color);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.1);
            }

            100% {
                transform: scale(1);
            }
        }

        .mobile-logout {
            display: none;
        }

        .custom-logout-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: linear-gradient(145deg, #f0b90b, #e6a908);
            border-radius: 50px;
            color: #000;
            transition: all 0.3s ease;
            border: 2px solid #f0b90b;
        }

        .custom-logout-btn:hover {
            background: #000;
            color: #f0b90b;
            border-color: #f0b90b;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(240, 185, 11, 0.2);
        }

        .logout-icon-wrapper {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logout-text {
            font-weight: 500;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        @media screen and (max-width: 768px) {
            .logout-text {
                display: none;
            }

            .custom-logout-btn {
                padding: 8px;
            }
        }

        .logout-btn i {
            font-size: 16px;
        }

        @media screen and (max-width: 768px) {
            .mobile-logout {
                display: block;
                position: fixed;
                bottom: 20px;
                right: 20px;
                z-index: 999;
            }

            .logout-btn {
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            }

            /* Hide desktop logout if it exists */
            .desktop-logout {
                display: none;
            }
        }
        #page-wrapper {
    padding: 0px 11px 0px 2px !important;
    min-height: 568px;
}
#wrapper {
    position: relative;
    margin-top: 69px;
}
        @media screen and (max-width: 480px) {
            .mobile-logout {
                bottom: 15px;
                right: 15px;
            }

            .logout-btn {
                padding: 8px 12px;
            }

            .logout-btn span {
                display: none;
            }

            .logout-btn i {
                font-size: 20px;
            }
        }

        .navbar-right {
            position: absolute;
            right: 0;
        }

        .navbar-top-links>li.log_out a {
            padding: 15px 15px !important;
        }
    </style>
</head>

<body>
    <div id="wrapper" class="wrapper animsition">
        <!-- Navigation -->
        <nav class="navbar navbar-fixed-top">
            <!-- <div class="navbar-header"> -->
            <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target=".navbar-collapse"
                aria-expanded="true">
                <span class="sr-only">Toggle navigation</span>
                <i class="material-icons">apps</i>
            </button>
             <a class="navbar-brand" href="dashboard.php">
                        <?php if(file_exists('images/Invesccoglobal_logo.png')){?>
                        <img class="main-logo" src="images/Invesccoglobal_logo.png" id="bg" style="    width: 137px;
    height: 110px;" alt="<?php echo SITE_NAME; ?>">
                        <?php }else{?>
                        <span><?php echo SITE_NAME; ?></span>
                        <?php }?>
                    </a>
            <!--<ul class="nav navbar-nav hidden-xs">-->
            <!--    <li><a id="fullscreen" href="#"><i class="material-icons">fullscreen</i> </a></li>-->
            <!--</ul>-->
            <ul class="nav navbar-top-links navbar-right">
                <li class="dropdown">
                    <a class="dropdown-toggle" href="email_inbox.php">
                        <i class="material-icons">chat</i>
                        <span class="label label-danger"><?php echo get_unread_message_count($uid); ?></span>
                    </a>
                </li><!-- /.Dropdown -->
                <?php /*<li class="dropdown">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</li>
                   <li class="dropdown">
                       <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:void(0);">
                           <i class="material-icons">person_add</i>
                       </a>
                       <ul class="dropdown-menu dropdown-user">
                           <?php /*<li><a href="profile.php"><i class="ti-user"></i>&nbsp; Profile</a></li>*/ ?>
                <?php /*<li><a href="report_login.php"><i class="ti-lock"></i>&nbsp; Login Details</a></li>*?>
                           <li><a href="logout.php"><i class="ti-layout-sidebar-left"></i>&nbsp; Logout</a></li>
                       </ul><!-- /.dropdown-user -->
                   </li><!-- /.Dropdown -->*/ ?>
                <li class="log_out">
                    <a href="logout.php" class="custom-logout-btn">
                        <span class="logout-icon-wrapper">
                            <i class="fas fa-sign-out-alt"></i>
                        </span>
                    </a>
                </li><!-- /.Log out -->
            </ul> <!-- /.navbar-top-links -->
            <!-- </div> -->

        </nav>
        <!-- /.Navigation -->
        <div class=" sidebar sidebar-nav navbar-collapse">
            <div class="sidebar-nav navbar-collapse">
                <ul class="nav" id="side-menu">

                    <li>
                        <a href="dashboard.php" data-title="Dashboard">
                            <i class="fas fa-home"></i>
                            <span class="menu-text">Dashboard</span>
                        </a>
                    </li>
                    <!--<li><a href="dashboard2.php" class="material-ripple"><i class="fas fa-chart-line"></i> Validator Dashboard</a></li>-->
                    
                    <!-- Profile Section -->
                    <li>
                        <a href="#" class="material-ripple"><i class="fas fa-user-circle"></i> Profile<span
                                class="fa arrow"></span></a>
                        <ul class="nav nav-second-level">
                            <li><a href="profile.php"><i class="fas fa-user"></i> My Profile</a></li>
                            <li><a href="change_password.php"><i class="fas fa-lock"></i> Change Password</a></li>
                        </ul>
                    </li>

                    <li>
                        <a href="#" class="material-ripple"><i class="fas fa-users"></i> Team<span
                                class="fa arrow"></span></a>
                        <ul class="nav nav-second-level">
                            <li><a href="direct_referral.php" class="material-ripple"><i class="fas fa-directions"></i>
                                    My Direct</a></li>
                            <li><a href="downline.php" class="material-ripple"><i class="fas fa-users"></i> My Team</a>
                            </li>
                            <?php /*<li><a href="tree_view.php" class="material-ripple"><i class="material-icons">bubble_chart</i> My Tree</a></li>*/ ?>
                        </ul>
                    </li>

                     <li>
                        <a href="invest.php" data-title="Trade">
                            <i class="fas fa-chart-bar icon"></i>
                            <span class="menu-text">Trade</span>
                        </a>
                    </li>

                    <!--<li><a href="invest_new.php?type=3" class="material-ripple"><i class="fas fa-money-bill"></i> Stacking Coins</a></li>-->

                    <li>
                        <a href="#" class="material-ripple"><i class="fa fa-usd" aria-hidden="true"></i> Trading
                            Reports<span class="fa arrow"></span></a>
                        <ul class="nav nav-second-level">
                                <li><a href="report_invest.php" class="material-ripple"><i class="fas fa-money-check-alt"></i> Investments History</a></li>
                                <li><a href="report_growth.php" class="material-ripple"><i class="fa fa-usd" aria-hidden="true"></i> ROI Income</a></li>
                                <li><a href="report_direct.php" class="material-ripple"><i class="fa fa-usd" aria-hidden="true"></i> Referral Income</a></li>
                                <li><a href="report_level.php?type=2" class="material-ripple"><i class="fa fa-usd" aria-hidden="true"></i>Level ROI Income</a></li>
                                <?php /*<li><a href="report_level.php" class="material-ripple"><i class="fa fa-usd" aria-hidden="true"></i>Mentor Reward Income</a></li>
                                <li><a href="report_level.php?type=1" class="material-ripple"><i class="fa fa-usd" aria-hidden="true"></i>Upline Income</a></li>
                                <li><a href="report_royalty.php" class="material-ripple"><i class="fa fa-usd" aria-hidden="true"></i>Active Team Reward Income</a></li>
                                <li><a href="report_royalty.php?type=1" class="material-ripple"><i class="fa fa-usd" aria-hidden="true"></i>Booster Income</a></li>
                                <li><a href="report_royalty.php?type=2" class="material-ripple"><i class="fa fa-usd" aria-hidden="true"></i>Special Reward Income</a></li>*/?>

                            <?php /*<li><a href="report_royalty.php?type=2" class="material-ripple"><i class="material-icons">business</i> Airdrop Income</a></li>
                           <li><a href="report_direct.php?type=2" class="material-ripple"><i class="material-icons">business</i> Referral Airdrop Rewad Income</a></li>
                           <li><a href="report_level.php?type=1" class="material-ripple"><i class="material-icons">business</i> Level Airdrop Income</a></li>*/ ?>

                            
                        </ul>
                    </li>

                    <!--<li>-->
                    <!--    <a href="#" class="material-ripple"><i class="fa fa-video-camera" aria-hidden="true"></i> Adding-->
                    <!--        Videos<span class="fa arrow"></span></a>-->
                    <!--    <ul class="nav nav-second-level">-->
                    <!--        <li><a href="video.php" class="material-ripple"><i class="fa fa-plus-circle"-->
                    <!--                    aria-hidden="true"></i> Submit Video</a></li>-->
                    <!--        <li><a href="report_videos.php" class="material-ripple"><i class="fa fa-video-camera"-->
                    <!--                    aria-hidden="true"></i> Videos</a></li>-->
                    <!--    </ul>-->
                    <!--</li>-->

                  <li>
                            <a href="#" class="material-ripple"><i class="fas fa-exchange-alt"></i> Fund Management<span class="fa arrow"></span></a>
                            <ul class="nav nav-second-level">
                                <?php /*<li><a href="deposit_block.php" class="material-ripple"><i class="fas fa-cart-plus"></i> Add Fund by <?php echo SITE_CURRENCY_TKN; ?></a></li>*/?>
                                <li><a href="deposit_block.php" class="material-ripple"><i class="fas fa-cart-plus"></i> Add Fund by <?php echo SITE_CURRENCY_TKN; ?></a></li>
                                <li><a href="report_deposit_block.php" class="material-ripple"><i class="fas fa-history"></i> Deposit History</a></li>
                                <li><a href="withdrawal_block.php?type=10" class="material-ripple"><i class="material-icons">insert_emoticon</i> Withdrawal</a></li>
                                <li><a href="report_withdrawal_block.php" class="material-ripple"><i class="fas fa-history"></i> Withdrawal History</a></li>
                            </ul>
                        </li>

                 <li>
                            <a href="#" class="material-ripple"><i class="fas fa-money-bill-alt"></i> Fund Transfer<span class="fa arrow"></span></a>
                            <ul class="nav nav-second-level">
                                <!--<li><a href="fund_transfer.php?type=1" class="material-ripple"><i class="fas fa-exchange-alt"></i> Topup Fund Transfer</a></li>-->
                                <!--<li><a href="fund_transfer3.php?type=1" class="material-ripple"><i class="fas fa-exchange-alt"></i> Self Fund Transfer</a></li>-->
                                <?php /*<li><a href="fund_transfer2.php" class="material-ripple"><i class="fas fa-exchange-alt"></i> Fund Transfer to Game</a></li>*/?>
                                <li><a href="report_fund_transfer.php" class="material-ripple"><i class="fas fa-history"></i> Fund Transfer History</a></li>
                            </ul>
                        </li>
                    <!--<li>-->
                    <!--    <a href="report_deposit_block.php" data-title="Deposit History">-->
                    <!--        <i class="fas fa-history">-->
                    <!--        <span class="menu-text">Dashboard</span>-->
                    <!--    </a>-->
                    <!--</li>-->
                   
                    <li>
                        <a href="#" class="material-ripple"><i class="fas fa-mail-bulk"></i> Support<span
                                class="fa arrow"></span></a>
                        <ul class="nav nav-second-level">
                                <li><a href="email_compose_mail.php">Compose</a></li>
                                <li><a href="email_inbox.php">Inbox</a></li>
                                <li><a href="email_sent_mail.php">Sent</a></li>
                            </ul>
                    </li>
                </ul>
            </div>
            <!-- /.sidebar-collapse -->
        </div>
        <!-- /.Left Sidebar-->
        <!-- /.Navbar  Static Side -->
        <div class="control-sidebar-bg"></div>
        <!-- Page Content -->
        <div id="page-wrapper">
            <!-- main content -->
            <div class="content">
                <?php /*<div class="row">
                   <div class="col-sm-12 col-md-6">
                       <a style="cursor:pointer;color:#fff;border-radius:10px;padding: 5px 10px;align-items: center;display:inline-flex;background: #5b69bc;margin-top: 10px;margin-bottom: 10px;" href="<?php echo (SITE_CURRENCY_ == 'TRX') ? 'https://tronscan.org/#/contract' : ((SITE_CURRENCY_ == 'BNB') ? 'https://bscscan.com/address' : 'https://etherscan.io/address'); ?>/<?php echo CONTRACT_ADDRESS;?>" target="_blank">Contract Address: <?php echo CONTRACT_ADDRESS;?> <i class="fa fa-external-link"></i></a>
                   </div>
                   <div class="col-sm-12 col-md-6">
                       <a style="cursor:pointer;color:#fff;border-radius:10px;padding: 5px 10px;align-items: center;display:inline-flex;background: #5b69bc;margin-top: 10px;margin-bottom: 10px;" href="<?php echo (SITE_CURRENCY_ == 'TRX') ? 'https://tronscan.org/#/address' : ((SITE_CURRENCY_ == 'BNB') ? 'https://bscscan.com/address' : 'https://etherscan.io/address'); ?>/<?php echo $user->bnb_address;?>" target="_blank">Your Address: <?php echo $user->bnb_address;?> <i class="fa fa-external-link"></i></a>
                   </div>
               </div>*/ ?>
                <!-- Content Header (Page header) -->
                <div class="content-header">
                    <div class="header-icon"><i
                            class="pe-7s-<?php echo isset($titleicon) ? $titleicon : 'graph1'; ?>"></i></div>
                    <div class="header-title">
                        <h1><?php echo isset($title) ? $title : ''; ?></h1>
                    </div>
                </div> <!-- /. Content Header (Page header) -->
                <div class="row">
                    <div class="col-sm-12 col-md-12">
                        <?php echo getMessage(); ?>
                    </div>
                </div>
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        // Add has-dropdown class to menu items with dropdowns
                        const menuItems = document.querySelectorAll('#side-menu li');
                        menuItems.forEach(item => {
                            if (item.querySelector('.nav-second-level')) {
                                item.classList.add('has-dropdown');
                            }
                        });

                        // Handle mobile responsiveness
                        function adjustForMobile() {
                            const isMobile = window.innerWidth < 768;
                            const sidebar = document.querySelector('.sidebar');
                            
                            if (isMobile) {
                                sidebar.classList.remove('expanded');
                            }
                        }

                        window.addEventListener('resize', adjustForMobile);
                        adjustForMobile();
                    });
                </script>
                <script>
                    document.addEventListener('DOMContentLoaded', function () {
                        // Theme Switcher
                        const themeSwitcher = document.getElementById('themeSwitcher');
                        const html = document.documentElement;

                        themeSwitcher.addEventListener('click', function () {
                            const currentTheme = html.getAttribute('data-theme');
                            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                            html.setAttribute('data-theme', newTheme);

                            // Update icon
                            const icon = this.querySelector('i');
                            icon.textContent = newTheme === 'dark' ? 'dark_mode' : 'light_mode';

                            // Save preference
                            localStorage.setItem('theme', newTheme);
                        });

                        // Settings Panel Toggle
                        const settingsToggle = document.getElementById('settingsToggle');
                        const settingsPanel = document.getElementById('settingsPanel');

                        settingsToggle.addEventListener('click', function (e) {
                            e.preventDefault();
                            settingsPanel.classList.toggle('active');
                        });

                        // Sidebar Toggle
                        const sidebarToggle = document.getElementById('sidebarToggle');
                        const sidebar = document.querySelector('.sidebar');

                        sidebarToggle.addEventListener('click', function () {
                            sidebar.classList.toggle('active');
                        });

                        // Add slide-in animation to menu items
                        const menuItems = document.querySelectorAll('#side-menu li a');
                        menuItems.forEach((item, index) => {
                            item.style.animationDelay = `${index * 0.1}s`;
                            item.classList.add('slide-in');
                        });

                        // Load saved theme preference
                        const savedTheme = localStorage.getItem('theme') || 'dark';
                        html.setAttribute('data-theme', savedTheme);
                        themeSwitcher.querySelector('i').textContent =
                            savedTheme === 'dark' ? 'dark_mode' : 'light_mode';
                    });
                </script>
                <style>
                /* Base styles */
                .nav-second-level {
                    list-style: none;
                    padding: 0;
                    margin: 0;
                    background: var(--card-bg);
                    border: 1px solid rgba(255, 255, 255, 0.1);
                    border-radius: 4px;
                    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.2);
                }

                /* Desktop styles (hover) */
                @media (min-width: 769px) {
                    .nav-second-level {
                        position: absolute;
                        left: 100%;
                        top: 0;
                        min-width: 200px;
                        display: none;
                    }

                    #side-menu li:hover > .nav-second-level {
                        display: block;
                        animation: fadeInUp 0.3s ease forwards;
                    }
                }

                /* Mobile styles */
                @media (max-width: 768px) {
                    .nav-second-level {
                        position: static;
                        display: none;
                        background: rgba(0, 0, 0, 0.1);
                    }

                    .nav-second-level.show {
                        display: block;
                    }

                    #side-menu li a {
                        padding: 15px 20px; /* Larger touch target */
                    }

                    .nav-second-level li a {
                        padding-left: 40px; /* Indent submenu items */
                    }
                }

                /* Animation keyframes */
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(5px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }
                </style>
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    const isMobile = () => window.innerWidth <= 768;
                    const sidebar = document.querySelector('.sidebar');
                    
                    function adjustForMobile() {
                        if (isMobile()) {
                            // Force show menu text in mobile
                            const menuTexts = document.querySelectorAll('.menu-text');
                            menuTexts.forEach(text => {
                                text.style.display = 'block';
                                text.style.opacity = '1';
                            });

                            // Ensure sidebar is full width
                            sidebar.style.width = '100%';
                        } else {
                            // Reset to default desktop state
                            sidebar.style.width = '';
                            const menuTexts = document.querySelectorAll('.menu-text');
                            menuTexts.forEach(text => {
                                text.style.display = '';
                                text.style.opacity = '';
                            });
                        }
                    }

                    // Initial setup
                    adjustForMobile();

                    // Handle resize events
                    let resizeTimer;
                    window.addEventListener('resize', () => {
                        clearTimeout(resizeTimer);
                        resizeTimer = setTimeout(adjustForMobile, 250);
                    });

                    // Close submenus when clicking outside
                    document.addEventListener('click', (e) => {
                        if (isMobile() && !e.target.closest('#side-menu')) {
                            const allSubmenus = document.querySelectorAll('.nav-second-level');
                            allSubmenus.forEach(menu => menu.classList.remove('show'));
                        }
                    });
                });
                </script>
